import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ratingReminderService } from '../services/rating-reminder-service';

/**
 * Composable for settings-related functionality
 */
export function useSettings() {
  const router = useRouter();

  /**
   * Navigate to settings page
   */
  const navigateToSettings = () => {
    router.push('/settings');
  };

  /**
   * Get current rating reminder setting
   */
  const getRatingReminderEnabled = () => {
    return ratingReminderService.getUserSettingsEnabled();
  };

  /**
   * Update rating reminder setting
   */
  const setRatingReminderEnabled = async (enabled: boolean) => {
    await ratingReminderService.setUserSettingsEnabled(enabled);
  };

  /**
   * Get effective settings state
   */
  const getEffectiveSettings = () => {
    return ratingReminderService.getEffectiveSettings();
  };

  /**
   * Check if settings are available
   */
  const isSettingsAvailable = () => {
    return true; // Settings are always available
  };

  return {
    navigateToSettings,
    getRatingReminderEnabled,
    setRatingReminderEnabled,
    getEffectiveSettings,
    isSettingsAvailable,
  };
}

/**
 * Composable for reactive rating reminder settings
 */
export function useRatingReminderSettings() {
  const ratingEnabled = ref<boolean | null>(null);
  const isLoading = ref(false);

  let settingsChangeListener: ((enabled: boolean, previousValue: boolean | null) => void) | null = null;

  /**
   * Load current settings
   */
  const loadSettings = async () => {
    try {
      isLoading.value = true;
      ratingEnabled.value = ratingReminderService.getUserSettingsEnabled();
    } catch (error) {
      console.error('Failed to load rating reminder settings:', error);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Update settings
   */
  const updateSettings = async (enabled: boolean) => {
    try {
      isLoading.value = true;
      await ratingReminderService.setUserSettingsEnabled(enabled);
      ratingEnabled.value = enabled;
    } catch (error) {
      console.error('Failed to update rating reminder settings:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Setup reactive listener
   */
  const setupListener = () => {
    settingsChangeListener = (enabled: boolean) => {
      ratingEnabled.value = enabled;
    };
    ratingReminderService.addSettingsChangeListener(settingsChangeListener);
  };

  /**
   * Cleanup listener
   */
  const cleanupListener = () => {
    if (settingsChangeListener) {
      ratingReminderService.removeSettingsChangeListener(settingsChangeListener);
      settingsChangeListener = null;
    }
  };

  onMounted(() => {
    loadSettings();
    setupListener();
  });

  onUnmounted(() => {
    cleanupListener();
  });

  return {
    ratingEnabled,
    isLoading,
    loadSettings,
    updateSettings,
  };
}
