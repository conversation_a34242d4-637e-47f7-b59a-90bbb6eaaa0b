<template>
  <ion-app>
    <ion-router-outlet />

    <!-- Global Rating Reminder Modal -->
    <RatingReminderModal
      :is-open="isRatingModalOpen"
      :app-name="appName"
      @action="handleRatingAction"
      @dismiss="handleRatingDismiss"
    />
  </ion-app>
</template>

<script setup lang="ts">
import { IonApp, IonRouterOutlet } from '@ionic/vue';
import RatingReminderModal from './components/RatingReminderModal.vue';
import { useRatingReminderManager } from './services/rating-reminder-manager';
import { RatingReminderAction } from './services/rating-reminder-service';

// Use rating reminder manager
const {
  isModalOpen: isRatingModalOpen,
  appName,
  handleUserAction,
  hideReminder,
} = useRatingReminderManager();

// Handle rating actions
const handleRatingAction = async (action: RatingReminderAction) => {
  try {
    await handleUserAction(action);
  } catch (error) {
    console.error('Error handling rating action:', error);
    // Modal will be hidden by the manager even on error
  }
};

// Handle modal dismiss (should not normally happen since backdrop-dismiss is false)
const handleRatingDismiss = () => {
  console.log('Rating reminder modal dismissed');
  hideReminder();
};
</script>
