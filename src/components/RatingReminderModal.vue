<template>
  <ion-modal
    :is-open="isOpen"
    @did-dismiss="handleDismiss"
    :backdrop-dismiss="false"
    :keyboard-close="false"
    class="rating-reminder-modal"
    :aria-labelledby="modalTitleId"
    :aria-describedby="modalDescriptionId"
    role="dialog"
    aria-modal="true"
  >
    <div class="modal-content">
      <!-- Header with app icon/star -->
      <div class="modal-header">
        <ion-icon
          :icon="starOutline"
          class="star-icon"
          aria-hidden="true"
        ></ion-icon>
        <h2
          :id="modalTitleId"
          class="modal-title"
        >
          Rate {{ appName }}
        </h2>
      </div>

      <!-- Content -->
      <div class="modal-body">
        <p
          :id="modalDescriptionId"
          class="modal-message"
        >
          We hope you're enjoying {{ appName }}! Would you mind taking a moment to rate us?
          It helps us improve and reach more users like you.
        </p>

        <!-- Star rating display (visual only) -->
        <div
          class="star-rating"
          role="img"
          aria-label="Five star rating display"
        >
          <ion-icon
            v-for="star in 5"
            :key="star"
            :icon="star"
            class="rating-star"
            aria-hidden="true"
          ></ion-icon>
        </div>
      </div>

      <!-- Action buttons -->
      <div
        class="modal-actions"
        role="group"
        aria-label="Rating reminder actions"
      >
        <ion-button
          ref="dontAskButton"
          fill="clear"
          color="medium"
          @click="handleAction('dont_ask_again')"
          @keydown="handleKeyDown"
          class="action-button secondary-button"
          :disabled="isProcessing"
          :aria-label="`Don't ask again to rate ${appName}`"
          aria-describedby="dont-ask-description"
        >
          Don't Ask Again
          <span id="dont-ask-description" class="sr-only">
            This will permanently disable rating reminders
          </span>
        </ion-button>

        <ion-button
          ref="remindLaterButton"
          fill="clear"
          color="primary"
          @click="handleAction('remind_later')"
          @keydown="handleKeyDown"
          class="action-button secondary-button"
          :disabled="isProcessing"
          :aria-label="`Remind me later to rate ${appName}`"
          aria-describedby="remind-later-description"
        >
          Remind Me Later
          <span id="remind-later-description" class="sr-only">
            This will show the reminder again after a few more app launches
          </span>
        </ion-button>

        <ion-button
          ref="rateNowButton"
          fill="solid"
          color="primary"
          @click="handleAction('rate_now')"
          @keydown="handleKeyDown"
          class="action-button primary-button"
          :disabled="isProcessing"
          :aria-label="`Rate ${appName} now in the app store`"
          aria-describedby="rate-now-description"
          autofocus
        >
          <ion-icon :icon="starOutline" slot="start" aria-hidden="true"></ion-icon>
          Rate Now
          <span id="rate-now-description" class="sr-only">
            This will open the app store to leave a rating
          </span>
        </ion-button>
      </div>

      <!-- Loading indicator -->
      <div v-if="isProcessing" class="loading-overlay">
        <ion-spinner name="crescent"></ion-spinner>
      </div>
    </div>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import {
  IonModal,
  IonButton,
  IonIcon,
  IonSpinner,
} from '@ionic/vue';
import { starOutline, star } from 'ionicons/icons';
import { RatingReminderAction } from '../services/rating-reminder-service';

// Props
interface Props {
  isOpen: boolean;
  appName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  appName: 'this app'
});

// Emits
interface Emits {
  (e: 'action', action: RatingReminderAction): void;
  (e: 'dismiss'): void;
}

const emit = defineEmits<Emits>();

// State
const isProcessing = ref(false);
const rateNowButton = ref<HTMLElement>();
const remindLaterButton = ref<HTMLElement>();
const dontAskButton = ref<HTMLElement>();

// Computed
const appName = computed(() => props.appName || 'this app');
const modalTitleId = computed(() => `rating-modal-title-${Math.random().toString(36).substr(2, 9)}`);
const modalDescriptionId = computed(() => `rating-modal-desc-${Math.random().toString(36).substr(2, 9)}`);

// Methods
const handleAction = async (actionType: string) => {
  if (isProcessing.value) return;

  try {
    isProcessing.value = true;

    let action: RatingReminderAction;
    switch (actionType) {
      case 'rate_now':
        action = RatingReminderAction.RATE_NOW;
        break;
      case 'remind_later':
        action = RatingReminderAction.REMIND_LATER;
        break;
      case 'dont_ask_again':
        action = RatingReminderAction.DONT_ASK_AGAIN;
        break;
      default:
        console.warn('Unknown action type:', actionType);
        return;
    }

    emit('action', action);
  } catch (error) {
    console.error('Error handling rating action:', error);
  } finally {
    isProcessing.value = false;
  }
};

const handleDismiss = () => {
  if (!isProcessing.value) {
    emit('dismiss');
  }
};

// Keyboard navigation
const handleKeyDown = (event: KeyboardEvent) => {
  if (isProcessing.value) return;

  switch (event.key) {
    case 'Escape':
      // Allow escape to dismiss if not processing
      handleDismiss();
      break;
    case 'Tab':
      handleTabNavigation(event);
      break;
    case 'Enter':
    case ' ':
      // Prevent default to avoid double-triggering
      event.preventDefault();
      (event.target as HTMLElement)?.click();
      break;
  }
};

const handleTabNavigation = (event: KeyboardEvent) => {
  const buttons = [dontAskButton.value, remindLaterButton.value, rateNowButton.value].filter(Boolean);
  const currentIndex = buttons.findIndex(button => button === event.target);

  if (currentIndex === -1) return;

  if (event.shiftKey) {
    // Shift+Tab - go to previous button
    event.preventDefault();
    const prevIndex = currentIndex === 0 ? buttons.length - 1 : currentIndex - 1;
    buttons[prevIndex]?.focus();
  } else {
    // Tab - go to next button
    event.preventDefault();
    const nextIndex = currentIndex === buttons.length - 1 ? 0 : currentIndex + 1;
    buttons[nextIndex]?.focus();
  }
};

// Focus management
const setInitialFocus = async () => {
  await nextTick();
  // Focus the primary action button by default
  rateNowButton.value?.focus();
};

const trapFocus = (event: KeyboardEvent) => {
  if (event.key !== 'Tab' || !props.isOpen) return;

  const focusableElements = [
    dontAskButton.value,
    remindLaterButton.value,
    rateNowButton.value
  ].filter(Boolean);

  if (focusableElements.length === 0) return;

  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  if (event.shiftKey) {
    if (document.activeElement === firstElement) {
      event.preventDefault();
      lastElement?.focus();
    }
  } else {
    if (document.activeElement === lastElement) {
      event.preventDefault();
      firstElement?.focus();
    }
  }
};

// Lifecycle
watch(() => props.isOpen, async (isOpen) => {
  if (isOpen) {
    await setInitialFocus();
    document.addEventListener('keydown', trapFocus);
  } else {
    document.removeEventListener('keydown', trapFocus);
  }
});

onMounted(() => {
  if (props.isOpen) {
    setInitialFocus();
    document.addEventListener('keydown', trapFocus);
  }
});

onUnmounted(() => {
  document.removeEventListener('keydown', trapFocus);
});
</script>

<style scoped>
.rating-reminder-modal {
  --backdrop-opacity: 0.4;
  --width: min(400px, calc(100vw - 40px));
  --height: auto;
  --border-radius: 16px;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-content {
  position: relative;
  background: var(--ion-background-color, #ffffff);
  border-radius: var(--border-radius, 16px);
  margin: 20px auto;
  max-width: 400px;
  width: calc(100% - 40px);
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--box-shadow, 0 10px 30px rgba(0, 0, 0, 0.2));

  /* Platform-specific adjustments */
  @supports (-webkit-backdrop-filter: blur(10px)) {
    backdrop-filter: blur(10px);
  }
}

.modal-header {
  text-align: center;
  padding: 32px 24px 16px;
  border-bottom: 1px solid var(--ion-color-light, #f4f5f8);
  background: linear-gradient(135deg,
    var(--ion-background-color, #ffffff) 0%,
    var(--ion-color-light, #f4f5f8) 100%);
}

.star-icon {
  font-size: 48px;
  color: var(--ion-color-warning, #ffc409);
  margin-bottom: 16px;
  filter: drop-shadow(0 2px 4px rgba(255, 196, 9, 0.3));
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.modal-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--ion-text-color, #000000);
  letter-spacing: -0.5px;
}

.modal-body {
  padding: 24px;
  text-align: center;
}

.modal-message {
  margin: 0 0 24px;
  font-size: 16px;
  line-height: 1.5;
  color: var(--ion-color-medium, #92949c);
}

.star-rating {
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
}

.rating-star {
  font-size: 24px;
  color: var(--ion-color-warning, #ffc409);
}

.modal-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 24px 24px;
}

.action-button {
  margin: 0;
  height: 48px;
  font-weight: 500;
  border-radius: 12px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  /* Ensure minimum touch target size */
  min-height: 44px;
  min-width: 44px;
}

.primary-button {
  --background: var(--ion-color-primary, #3880ff);
  --background-hover: var(--ion-color-primary-shade, #3171e0);
  --color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(56, 128, 255, 0.3);
}

.primary-button:hover {
  --background: var(--background-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(56, 128, 255, 0.4);
}

.secondary-button {
  --color: var(--ion-color-medium, #92949c);
  --background: transparent;
  --background-hover: var(--ion-color-light, #f4f5f8);
}

.secondary-button:hover {
  --color: var(--ion-color-primary, #3880ff);
  --background: var(--background-hover);
}

.secondary-button:active {
  --background: var(--ion-color-light-shade, #e9eaed);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: var(--ion-background-color, #1e1e1e);
  }

  .modal-title {
    color: var(--ion-text-color, #ffffff);
  }

  .loading-overlay {
    background: rgba(30, 30, 30, 0.8);
  }
}

/* Responsive design */
@media (min-width: 768px) {
  .modal-content {
    margin: 40px auto;
    max-width: 420px;
  }

  .modal-header {
    padding: 40px 32px 20px;
  }

  .modal-body {
    padding: 32px;
  }

  .modal-actions {
    flex-direction: row;
    justify-content: space-between;
    padding: 0 32px 32px;
    gap: 12px;
  }

  .action-button {
    flex: 1;
    max-width: 140px;
    height: 52px;
  }

  .primary-button {
    order: 3;
  }

  .secondary-button:first-child {
    order: 1;
  }

  .secondary-button:last-of-type {
    order: 2;
  }
}

/* Large screens */
@media (min-width: 1024px) {
  .modal-content {
    max-width: 480px;
  }
}

/* Platform-specific styles */
/* iOS-style rounded corners and shadows */
.ios .modal-content {
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.ios .action-button {
  border-radius: 14px;
  font-weight: 500;
}

.ios .primary-button {
  font-weight: 600;
}

/* Android Material Design */
.md .modal-content {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
}

.md .action-button {
  border-radius: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.md .modal-title {
  font-weight: 500;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modal-content {
    border: 2px solid var(--ion-color-medium, #92949c);
  }

  .action-button {
    border: 1px solid var(--ion-color-medium, #92949c);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modal-content {
    animation: none;
  }

  .action-button {
    transition: none;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.action-button:focus {
  outline: 2px solid var(--ion-color-primary, #3880ff);
  outline-offset: 2px;
}

.action-button:focus-visible {
  outline: 2px solid var(--ion-color-primary, #3880ff);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(56, 128, 255, 0.2);
}

/* Enhanced focus indicators for keyboard navigation */
.modal-content:focus-within {
  box-shadow: var(--box-shadow, 0 10px 30px rgba(0, 0, 0, 0.2)),
              0 0 0 2px var(--ion-color-primary, #3880ff);
}

/* Ensure proper contrast for text */
.modal-message {
  color: var(--ion-color-dark, #222428);
  line-height: 1.6;
}

/* Loading state accessibility */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  /* Announce loading state to screen readers */
  &::before {
    content: 'Processing your request...';
    position: absolute;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
  }
}

/* Animation */
.modal-content {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
