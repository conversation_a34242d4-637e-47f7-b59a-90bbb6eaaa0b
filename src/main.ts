import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import { SplashScreen } from "@capacitor/splash-screen";
import { pushNotificationService } from "./services/push-notification-service";
import { ratingReminderService } from "./services/rating-reminder-service";
import { ratingReminderManager } from "./services/rating-reminder-manager";
import { ratingReminderTest } from "./utils/rating-reminder-test";
import { logInfo, logError, logWarn } from "./utils/rating-reminder-logger";
import { App as CapacitorApp } from "@capacitor/app";

import { IonicVue } from "@ionic/vue";

/* Core CSS required for Ionic components to work properly */
import "@ionic/vue/css/core.css";

/* Basic CSS for apps built with Ionic */
import "@ionic/vue/css/normalize.css";
import "@ionic/vue/css/structure.css";
import "@ionic/vue/css/typography.css";

/* Optional CSS utils that can be commented out */
import "@ionic/vue/css/padding.css";
import "@ionic/vue/css/float-elements.css";
import "@ionic/vue/css/text-alignment.css";
import "@ionic/vue/css/text-transformation.css";
import "@ionic/vue/css/flex-utils.css";
import "@ionic/vue/css/display.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import '@ionic/vue/css/palettes/dark.always.css'; */
/* @import '@ionic/vue/css/palettes/dark.class.css'; */
import "@ionic/vue/css/palettes/dark.system.css";

/* Theme variables */
import "./theme/variables.css";

const app = createApp(App).use(IonicVue).use(router);

router.isReady().then(async () => {
  await SplashScreen.show({
    autoHide: false,
  });
  app.mount("#app");

  // Initialize push notifications
  if (pushNotificationService.isSupported()) {
    try {
      await pushNotificationService.initialize();
      console.log('Push notification service initialized');

      // Process any pending background data
      await pushNotificationService.processBackgroundData();
      console.log('Background data processed');
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
    }
  }

  // Initialize rating reminder service
  try {
    logInfo('Initializing rating reminder service', { component: 'main' });
    await ratingReminderService.initialize();
    logInfo('Rating reminder service initialized successfully', { component: 'main' });

    // Track app launch and check if reminder should be shown
    const shouldShowReminder = await ratingReminderService.trackLaunch();
    if (shouldShowReminder) {
      logInfo('Rating reminder should be shown', { component: 'main' });
      // Schedule reminder to show after app is fully loaded
      await ratingReminderManager.checkAndShowReminder(3000); // 3 second delay
    }
  } catch (error) {
    logError('Failed to initialize rating reminder service', error as Error, { component: 'main' });
  }

  // Set up app state change listeners for background processing
  CapacitorApp.addListener('appStateChange', async ({ isActive }) => {
    if (isActive) {
      // Process push notification background data
      if (pushNotificationService.isSupported()) {
        try {
          await pushNotificationService.processBackgroundData();
          console.log('Background data processed on app activation');
        } catch (error) {
          console.error('Failed to process background data on activation:', error);
        }
      }

      // Track app launch for rating reminder (when returning from background)
      try {
        logInfo('App became active, tracking launch for rating reminder', { component: 'main' });
        const shouldShowReminder = await ratingReminderService.trackLaunch();
        if (shouldShowReminder) {
          logInfo('Rating reminder should be shown after app activation', { component: 'main' });
          // Schedule reminder with a longer delay when returning from background
          await ratingReminderManager.checkAndShowReminder(5000); // 5 second delay
        }
      } catch (error) {
        logError('Failed to track launch on app activation', error as Error, { component: 'main' });
      }
    }
  });

  // Hide splash screen
  await SplashScreen.hide();

  // Add global test functions for development
  if (import.meta.env.DEV) {
    (window as any).ratingReminderTest = {
      runAllTests: () => ratingReminderTest.runAllTests(),
      getDebugInfo: () => ratingReminderTest.getDebugInfo(),
      service: ratingReminderService,
      manager: ratingReminderManager,
      forceShow: () => ratingReminderManager.forceShowReminder(),
      reset: () => ratingReminderService.resetState(),
      simulate: (count: number) => ratingReminderService.simulateLaunches(count)
    };
    console.log('🧪 Rating Reminder Test utilities available at window.ratingReminderTest');
  }
});
