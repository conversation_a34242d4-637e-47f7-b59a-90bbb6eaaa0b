import { Preferences } from "@capacitor/preferences";
import { AppReview } from "@capawesome/capacitor-app-review";

// TypeScript Interfaces for Rating Reminder Management
export interface RatingReminderConfig {
  enabled: boolean;
  launchThreshold: number;
  snoozeLaunches: number;
}

export interface RatingReminderState {
  launchCount: number;
  lastShownTimestamp: number;
  userPreference: UserRatingPreference;
  snoozeCount: number;
  hasRated: boolean;
  version: string;
}

export enum UserRatingPreference {
  NOT_SET = "not_set",
  REMIND_LATER = "remind_later", 
  DONT_ASK_AGAIN = "dont_ask_again",
  RATED = "rated"
}

export enum RatingReminderAction {
  RATE_NOW = "rate_now",
  REMIND_LATER = "remind_later",
  DONT_ASK_AGAIN = "dont_ask_again"
}

/**
 * RatingReminderService
 * 
 * Manages app rating reminders with configurable thresholds, user preferences,
 * and persistent storage. Integrates with native app store review functionality.
 */
export class RatingReminderService {
  private static readonly STORAGE_KEY = "rating_reminder_state";
  private static readonly SETTINGS_KEY = "rating_reminder_settings";
  private static readonly CURRENT_VERSION = "1.0.0";

  private static readonly DEFAULT_CONFIG: RatingReminderConfig = {
    enabled: import.meta.env.VITE_RATING_REMINDER_ENABLED === "true",
    launchThreshold: parseInt(import.meta.env.VITE_RATING_REMINDER_LAUNCH_THRESHOLD) || 5,
    snoozeLaunches: parseInt(import.meta.env.VITE_RATING_REMINDER_SNOOZE_LAUNCHES) || 3,
  };

  private static readonly DEFAULT_STATE: RatingReminderState = {
    launchCount: 0,
    lastShownTimestamp: 0,
    userPreference: UserRatingPreference.NOT_SET,
    snoozeCount: 0,
    hasRated: false,
    version: RatingReminderService.CURRENT_VERSION,
  };

  private config: RatingReminderConfig = RatingReminderService.DEFAULT_CONFIG;
  private state: RatingReminderState = { ...RatingReminderService.DEFAULT_STATE };
  private userSettingsEnabled: boolean | null = null;
  private isInitialized = false;
  private settingsChangeListeners: Array<(enabled: boolean, previousValue: boolean | null) => void> = [];

  constructor() {
    // Constructor is lightweight, actual initialization happens in initialize()
  }

  /**
   * Initialize the rating reminder service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadConfig();
      await this.loadState();
      await this.loadUserSettings();
      this.isInitialized = true;
      console.log("RatingReminderService initialized", {
        config: this.config,
        state: this.state,
        userSettingsEnabled: this.userSettingsEnabled
      });
    } catch (error) {
      console.error("Failed to initialize rating reminder service:", error);
      throw error;
    }
  }

  /**
   * Track an app launch and determine if reminder should be shown
   */
  async trackLaunch(): Promise<boolean> {
    if (!this.isInitialized) {
      console.warn("RatingReminderService not initialized, skipping launch tracking");
      return false;
    }

    if (!this.shouldTrackLaunches()) {
      return false;
    }

    try {
      // Increment launch count
      this.state.launchCount++;

      // Attempt to save state with retry logic
      const saveSuccess = await this.saveStateWithRetry();
      if (!saveSuccess) {
        console.warn("Failed to save launch count, continuing with in-memory state");
      }

      console.log(`Launch tracked: ${this.state.launchCount}/${this.config.launchThreshold}`);

      // Check if reminder should be shown
      return this.shouldShowReminder();
    } catch (error) {
      console.error("Failed to track launch:", error);
      // Don't decrement launch count on error - better to over-count than under-count
      return false;
    }
  }

  /**
   * Handle user action from rating reminder modal
   */
  async handleUserAction(action: RatingReminderAction): Promise<void> {
    try {
      console.log("Handling user action:", action);

      switch (action) {
        case RatingReminderAction.RATE_NOW:
          await this.handleRateNow();
          break;
        case RatingReminderAction.REMIND_LATER:
          await this.handleRemindLater();
          break;
        case RatingReminderAction.DONT_ASK_AGAIN:
          await this.handleDontAskAgain();
          break;
        default:
          console.warn("Unknown rating reminder action:", action);
      }

      await this.saveState();
    } catch (error) {
      console.error("Failed to handle user action:", error);
      throw error;
    }
  }

  /**
   * Check if reminder should be shown based on current state
   */
  shouldShowReminder(): boolean {
    if (!this.isEnabled()) {
      console.log("Rating reminder disabled");
      return false;
    }

    // Don't show if user has already rated or opted out
    if (this.state.userPreference === UserRatingPreference.RATED ||
        this.state.userPreference === UserRatingPreference.DONT_ASK_AGAIN) {
      console.log("User has rated or opted out:", this.state.userPreference);
      return false;
    }

    // Check launch threshold
    if (this.state.launchCount < this.config.launchThreshold) {
      console.log(`Launch threshold not met: ${this.state.launchCount}/${this.config.launchThreshold}`);
      return false;
    }

    // If user chose "remind later", check snooze period
    if (this.state.userPreference === UserRatingPreference.REMIND_LATER) {
      const launchesSinceSnooze = this.state.launchCount - this.state.snoozeCount;
      if (launchesSinceSnooze < this.config.snoozeLaunches) {
        console.log(`Still in snooze period: ${launchesSinceSnooze}/${this.config.snoozeLaunches} launches since snooze`);
        return false;
      } else {
        console.log(`Snooze period completed: ${launchesSinceSnooze}/${this.config.snoozeLaunches} launches since snooze`);
      }
    }

    console.log("Rating reminder should be shown");
    return true;
  }

  /**
   * Get current configuration (read-only)
   */
  getConfig(): Readonly<RatingReminderConfig> {
    return { ...this.config };
  }

  /**
   * Get current state (read-only)
   */
  getState(): Readonly<RatingReminderState> {
    return { ...this.state };
  }

  /**
   * Check if rating reminders are enabled
   */
  isEnabled(): boolean {
    // User settings override environment configuration
    if (this.userSettingsEnabled !== null) {
      return this.userSettingsEnabled && this.config.enabled;
    }
    return this.config.enabled;
  }

  /**
   * Set user preference for rating reminders (from settings)
   */
  async setUserSettingsEnabled(enabled: boolean): Promise<void> {
    try {
      const previousValue = this.userSettingsEnabled;
      this.userSettingsEnabled = enabled;

      await Preferences.set({
        key: RatingReminderService.SETTINGS_KEY,
        value: JSON.stringify({
          enabled,
          timestamp: Date.now(),
          version: RatingReminderService.CURRENT_VERSION
        })
      });

      console.log("User rating reminder setting updated:", {
        enabled,
        previousValue,
        effectivelyEnabled: this.isEnabled()
      });

      // Notify any listeners about the setting change
      this.notifySettingsChange(enabled, previousValue);

    } catch (error) {
      console.error("Failed to save user rating reminder setting:", error);
      // Revert the in-memory value on error
      this.userSettingsEnabled = this.userSettingsEnabled === enabled ? null : this.userSettingsEnabled;
      throw error;
    }
  }

  /**
   * Get user preference for rating reminders (for settings)
   */
  getUserSettingsEnabled(): boolean | null {
    return this.userSettingsEnabled;
  }

  /**
   * Add listener for settings changes
   */
  addSettingsChangeListener(listener: (enabled: boolean, previousValue: boolean | null) => void): void {
    this.settingsChangeListeners.push(listener);
  }

  /**
   * Remove settings change listener
   */
  removeSettingsChangeListener(listener: (enabled: boolean, previousValue: boolean | null) => void): void {
    const index = this.settingsChangeListeners.indexOf(listener);
    if (index > -1) {
      this.settingsChangeListeners.splice(index, 1);
    }
  }

  /**
   * Get effective settings state (considering both environment and user preferences)
   */
  getEffectiveSettings(): {
    environmentEnabled: boolean;
    userSettingsEnabled: boolean | null;
    effectivelyEnabled: boolean;
    source: 'environment' | 'user';
  } {
    const environmentEnabled = this.config.enabled;
    const userEnabled = this.userSettingsEnabled;
    const effectivelyEnabled = this.isEnabled();

    return {
      environmentEnabled,
      userSettingsEnabled: userEnabled,
      effectivelyEnabled,
      source: userEnabled !== null ? 'user' : 'environment'
    };
  }

  /**
   * Reset rating reminder state (for testing/debugging)
   */
  async resetState(): Promise<void> {
    try {
      this.state = { ...RatingReminderService.DEFAULT_STATE };
      await this.saveState();
      console.log("Rating reminder state reset");
    } catch (error) {
      console.error("Failed to reset rating reminder state:", error);
      throw error;
    }
  }

  /**
   * Simulate launch count for testing
   */
  async simulateLaunches(count: number): Promise<void> {
    if (!this.isInitialized) {
      throw new Error("Service not initialized");
    }

    try {
      this.state.launchCount = Math.max(0, count);
      await this.saveStateWithRetry();
      console.log(`Simulated launch count set to: ${this.state.launchCount}`);
    } catch (error) {
      console.error("Failed to simulate launches:", error);
      throw error;
    }
  }

  /**
   * Check if reminder would show at a specific launch count
   */
  wouldShowAtLaunchCount(launchCount: number): boolean {
    const originalCount = this.state.launchCount;
    this.state.launchCount = launchCount;

    const result = this.shouldShowReminder();

    // Restore original count
    this.state.launchCount = originalCount;

    return result;
  }

  /**
   * Load configuration from environment variables
   */
  private async loadConfig(): Promise<void> {
    this.config = { ...RatingReminderService.DEFAULT_CONFIG };
  }

  /**
   * Load state from persistent storage
   */
  private async loadState(): Promise<void> {
    try {
      const result = await Preferences.get({ key: RatingReminderService.STORAGE_KEY });
      if (result.value) {
        const savedState = JSON.parse(result.value) as RatingReminderState;

        // Validate and migrate state if needed
        if (this.isValidState(savedState)) {
          this.state = { ...RatingReminderService.DEFAULT_STATE, ...savedState };

          // Ensure version is current
          if (this.state.version !== RatingReminderService.CURRENT_VERSION) {
            this.state.version = RatingReminderService.CURRENT_VERSION;
            console.log("State version updated to", RatingReminderService.CURRENT_VERSION);
          }

          // Validate numeric values are within reasonable bounds
          this.state.launchCount = Math.max(0, Math.min(this.state.launchCount, 10000));
          this.state.snoozeCount = Math.max(0, Math.min(this.state.snoozeCount, 10000));

        } else {
          console.warn("Invalid saved state, using defaults");
          this.state = { ...RatingReminderService.DEFAULT_STATE };
        }
      } else {
        console.log("No saved state found, using defaults");
        this.state = { ...RatingReminderService.DEFAULT_STATE };
      }
    } catch (error) {
      console.error("Failed to load rating reminder state:", error);
      this.state = { ...RatingReminderService.DEFAULT_STATE };
    }
  }

  /**
   * Save state to persistent storage
   */
  private async saveState(): Promise<void> {
    try {
      await Preferences.set({
        key: RatingReminderService.STORAGE_KEY,
        value: JSON.stringify(this.state)
      });
    } catch (error) {
      console.error("Failed to save rating reminder state:", error);
      throw error;
    }
  }

  /**
   * Save state with retry logic for better reliability
   */
  private async saveStateWithRetry(maxRetries: number = 3): Promise<boolean> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.saveState();
        return true;
      } catch (error) {
        console.warn(`Save attempt ${attempt}/${maxRetries} failed:`, error);

        if (attempt === maxRetries) {
          console.error("All save attempts failed, state may be lost");
          return false;
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
      }
    }
    return false;
  }

  /**
   * Get launch count statistics for debugging
   */
  getLaunchStats(): {
    currentCount: number;
    threshold: number;
    remaining: number;
    shouldShow: boolean;
  } {
    return {
      currentCount: this.state.launchCount,
      threshold: this.config.launchThreshold,
      remaining: Math.max(0, this.config.launchThreshold - this.state.launchCount),
      shouldShow: this.shouldShowReminder()
    };
  }

  /**
   * Get comprehensive reminder status for debugging and UI
   */
  getReminderStatus(): {
    isEnabled: boolean;
    isInitialized: boolean;
    shouldTrack: boolean;
    shouldShow: boolean;
    config: RatingReminderConfig;
    state: RatingReminderState;
    userSettingsEnabled: boolean | null;
    blockingReasons: string[];
  } {
    const blockingReasons: string[] = [];

    if (!this.isInitialized) {
      blockingReasons.push("Service not initialized");
    }

    if (!this.isEnabled()) {
      blockingReasons.push("Feature disabled");
    }

    if (this.state.userPreference === UserRatingPreference.RATED) {
      blockingReasons.push("User has already rated");
    }

    if (this.state.userPreference === UserRatingPreference.DONT_ASK_AGAIN) {
      blockingReasons.push("User opted out");
    }

    if (this.state.launchCount < this.config.launchThreshold) {
      blockingReasons.push(`Launch threshold not met (${this.state.launchCount}/${this.config.launchThreshold})`);
    }

    if (this.state.userPreference === UserRatingPreference.REMIND_LATER) {
      const launchesSinceSnooze = this.state.launchCount - this.state.snoozeCount;
      if (launchesSinceSnooze < this.config.snoozeLaunches) {
        blockingReasons.push(`In snooze period (${launchesSinceSnooze}/${this.config.snoozeLaunches})`);
      }
    }

    return {
      isEnabled: this.isEnabled(),
      isInitialized: this.isInitialized,
      shouldTrack: this.shouldTrackLaunches(),
      shouldShow: this.shouldShowReminder(),
      config: this.getConfig(),
      state: this.getState(),
      userSettingsEnabled: this.userSettingsEnabled,
      blockingReasons
    };
  }

  /**
   * Force show reminder (for testing purposes)
   */
  async forceShowReminder(): Promise<boolean> {
    if (!this.isInitialized) {
      console.warn("Service not initialized, cannot force show reminder");
      return false;
    }

    console.log("Forcing reminder to show (testing mode)");

    // Temporarily override state to meet all conditions
    const originalState = { ...this.state };

    this.state.launchCount = Math.max(this.state.launchCount, this.config.launchThreshold);
    this.state.userPreference = UserRatingPreference.NOT_SET;

    const shouldShow = this.shouldShowReminder();

    // Restore original state
    this.state = originalState;

    return shouldShow;
  }

  /**
   * Load user settings from persistent storage
   */
  private async loadUserSettings(): Promise<void> {
    try {
      const result = await Preferences.get({ key: RatingReminderService.SETTINGS_KEY });
      if (result.value) {
        const settings = JSON.parse(result.value);

        // Validate settings structure
        if (typeof settings.enabled === 'boolean') {
          this.userSettingsEnabled = settings.enabled;

          // Check if settings need migration
          if (!settings.version || settings.version !== RatingReminderService.CURRENT_VERSION) {
            console.log('Migrating user settings to current version');
            await this.migrateUserSettings(settings);
          }
        } else {
          console.warn('Invalid user settings format, resetting to null');
          this.userSettingsEnabled = null;
        }
      } else {
        console.log('No user settings found, using environment defaults');
        this.userSettingsEnabled = null;
      }
    } catch (error) {
      console.error("Failed to load user rating reminder settings:", error);
      this.userSettingsEnabled = null;
    }
  }

  /**
   * Migrate user settings to current version
   */
  private async migrateUserSettings(oldSettings: any): Promise<void> {
    try {
      const newSettings = {
        enabled: oldSettings.enabled,
        timestamp: oldSettings.timestamp || Date.now(),
        version: RatingReminderService.CURRENT_VERSION
      };

      await Preferences.set({
        key: RatingReminderService.SETTINGS_KEY,
        value: JSON.stringify(newSettings)
      });

      console.log('User settings migrated successfully');
    } catch (error) {
      console.error('Failed to migrate user settings:', error);
    }
  }

  /**
   * Notify listeners about settings changes
   */
  private notifySettingsChange(enabled: boolean, previousValue: boolean | null): void {
    try {
      this.settingsChangeListeners.forEach(listener => {
        try {
          listener(enabled, previousValue);
        } catch (error) {
          console.error('Error in settings change listener:', error);
        }
      });
    } catch (error) {
      console.error('Error notifying settings change listeners:', error);
    }
  }

  /**
   * Validate state object structure
   */
  private isValidState(state: any): boolean {
    return state &&
           typeof state.launchCount === 'number' &&
           typeof state.lastShownTimestamp === 'number' &&
           Object.values(UserRatingPreference).includes(state.userPreference) &&
           typeof state.snoozeCount === 'number' &&
           typeof state.hasRated === 'boolean';
  }

  /**
   * Check if launches should be tracked
   */
  private shouldTrackLaunches(): boolean {
    return this.isEnabled() && 
           this.state.userPreference !== UserRatingPreference.RATED &&
           this.state.userPreference !== UserRatingPreference.DONT_ASK_AGAIN;
  }

  /**
   * Handle "Rate Now" action
   */
  private async handleRateNow(): Promise<void> {
    try {
      // Try in-app review first, fallback to app store
      await AppReview.requestReview();
      
      this.state.userPreference = UserRatingPreference.RATED;
      this.state.hasRated = true;
      this.state.lastShownTimestamp = Date.now();
      
      console.log("User chose to rate now - in-app review requested");
    } catch (error) {
      console.warn("In-app review failed, opening app store:", error);
      
      try {
        await AppReview.openAppStore();
        this.state.userPreference = UserRatingPreference.RATED;
        this.state.hasRated = true;
        this.state.lastShownTimestamp = Date.now();
        
        console.log("User chose to rate now - app store opened");
      } catch (storeError) {
        console.error("Failed to open app store:", storeError);
        throw storeError;
      }
    }
  }

  /**
   * Handle "Remind Me Later" action
   */
  private async handleRemindLater(): Promise<void> {
    this.state.userPreference = UserRatingPreference.REMIND_LATER;
    this.state.snoozeCount = this.state.launchCount;
    this.state.lastShownTimestamp = Date.now();

    console.log("User chose remind later - will show again after", this.config.snoozeLaunches, "more launches");
    console.log("Snooze baseline set to launch count:", this.state.snoozeCount);
  }

  /**
   * Handle "Don't Ask Again" action
   */
  private async handleDontAskAgain(): Promise<void> {
    this.state.userPreference = UserRatingPreference.DONT_ASK_AGAIN;
    this.state.lastShownTimestamp = Date.now();

    console.log("User chose don't ask again - rating reminders permanently disabled");
  }

  /**
   * Get user preference status for UI display
   */
  getUserPreferenceStatus(): {
    preference: UserRatingPreference;
    hasRated: boolean;
    isSnoozing: boolean;
    launchesUntilNextReminder: number;
    canShowReminder: boolean;
  } {
    const isSnoozing = this.state.userPreference === UserRatingPreference.REMIND_LATER;
    const launchesSinceSnooze = isSnoozing ? this.state.launchCount - this.state.snoozeCount : 0;
    const launchesUntilNext = isSnoozing ? Math.max(0, this.config.snoozeLaunches - launchesSinceSnooze) : 0;

    return {
      preference: this.state.userPreference,
      hasRated: this.state.hasRated,
      isSnoozing,
      launchesUntilNextReminder: launchesUntilNext,
      canShowReminder: this.shouldShowReminder()
    };
  }

  /**
   * Reset user preference (for testing or user request)
   */
  async resetUserPreference(): Promise<void> {
    try {
      this.state.userPreference = UserRatingPreference.NOT_SET;
      this.state.hasRated = false;
      this.state.snoozeCount = 0;
      this.state.lastShownTimestamp = 0;

      await this.saveStateWithRetry();
      console.log("User preference reset to default state");
    } catch (error) {
      console.error("Failed to reset user preference:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const ratingReminderService = new RatingReminderService();
