import { ref } from 'vue';
import { ratingReminderService, RatingReminderAction } from './rating-reminder-service';
import { logInfo, logError, logWarn, logUserAction, wrapOperation } from '../utils/rating-reminder-logger';

/**
 * Global Rating Reminder Manager
 * 
 * Manages the display and interaction with rating reminder modals
 * across the entire application.
 */
class RatingReminderManager {
  private static instance: RatingReminderManager;
  
  // Reactive state for modal visibility
  public isModalOpen = ref(false);
  public appName = ref('');
  
  // Pending reminder state
  private pendingReminder = false;
  private reminderTimeout: NodeJS.Timeout | null = null;

  /**
   * Get singleton instance
   */
  public static getInstance(): RatingReminderManager {
    if (!RatingReminderManager.instance) {
      RatingReminderManager.instance = new RatingReminderManager();
    }
    return RatingReminderManager.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    this.appName.value = import.meta.env.VITE_APP_NAME || 'this app';
  }

  /**
   * Check if reminder should be shown and show it if appropriate
   */
  public async checkAndShowReminder(delay: number = 2000): Promise<void> {
    return wrapOperation('checkAndShowReminder', async () => {
      // Don't show if already showing or pending
      if (this.isModalOpen.value || this.pendingReminder) {
        logInfo('Rating reminder check skipped - already showing or pending', {
          component: 'RatingReminderManager',
          isModalOpen: this.isModalOpen.value,
          isPending: this.pendingReminder
        });
        return;
      }

      // Check if reminder should be shown
      const shouldShow = ratingReminderService.shouldShowReminder();
      if (!shouldShow) {
        logInfo('Rating reminder not needed at this time', {
          component: 'RatingReminderManager'
        });
        return;
      }

      // Set pending state
      this.pendingReminder = true;

      // Show reminder after delay to avoid interrupting app startup
      this.reminderTimeout = setTimeout(() => {
        try {
          this.showReminder();
          this.pendingReminder = false;
        } catch (error) {
          logError('Error showing scheduled reminder', error as Error, {
            component: 'RatingReminderManager'
          });
          this.pendingReminder = false;
        }
      }, delay);

      logInfo(`Rating reminder scheduled to show in ${delay}ms`, {
        component: 'RatingReminderManager',
        delay
      });
    }, { component: 'RatingReminderManager' }).catch(error => {
      this.pendingReminder = false;
      throw error;
    });
  }

  /**
   * Show the rating reminder modal immediately
   */
  public showReminder(): void {
    if (this.isModalOpen.value) {
      console.warn('Rating reminder modal is already open');
      return;
    }

    console.log('Showing rating reminder modal');
    this.isModalOpen.value = true;
  }

  /**
   * Hide the rating reminder modal
   */
  public hideReminder(): void {
    console.log('Hiding rating reminder modal');
    this.isModalOpen.value = false;
    this.clearPendingReminder();
  }

  /**
   * Handle user action from the modal
   */
  public async handleUserAction(action: RatingReminderAction): Promise<void> {
    return wrapOperation('handleUserAction', async () => {
      logUserAction(action, {
        component: 'RatingReminderManager'
      });

      // Process the action through the service
      await ratingReminderService.handleUserAction(action);

      // Hide the modal
      this.hideReminder();

      logInfo('Rating reminder action processed successfully', {
        component: 'RatingReminderManager',
        action
      });
    }, {
      component: 'RatingReminderManager',
      action
    }).catch(error => {
      // Still hide the modal even if there was an error
      this.hideReminder();
      throw error;
    });
  }

  /**
   * Cancel any pending reminder
   */
  public cancelPendingReminder(): void {
    this.clearPendingReminder();
    console.log('Pending rating reminder cancelled');
  }

  /**
   * Force show reminder (for testing)
   */
  public async forceShowReminder(): Promise<boolean> {
    try {
      const canShow = await ratingReminderService.forceShowReminder();
      if (canShow) {
        this.showReminder();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error forcing rating reminder:', error);
      return false;
    }
  }

  /**
   * Get current reminder status
   */
  public getReminderStatus(): {
    isModalOpen: boolean;
    isPending: boolean;
    shouldShow: boolean;
    serviceStatus: any;
  } {
    return {
      isModalOpen: this.isModalOpen.value,
      isPending: this.pendingReminder,
      shouldShow: ratingReminderService.shouldShowReminder(),
      serviceStatus: ratingReminderService.getReminderStatus()
    };
  }

  /**
   * Clear pending reminder timeout
   */
  private clearPendingReminder(): void {
    if (this.reminderTimeout) {
      clearTimeout(this.reminderTimeout);
      this.reminderTimeout = null;
    }
    this.pendingReminder = false;
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    this.clearPendingReminder();
    this.isModalOpen.value = false;
  }
}

// Export singleton instance
export const ratingReminderManager = RatingReminderManager.getInstance();

/**
 * Composable for using rating reminder manager in Vue components
 */
export function useRatingReminderManager() {
  const manager = ratingReminderManager;

  return {
    isModalOpen: manager.isModalOpen,
    appName: manager.appName,
    showReminder: () => manager.showReminder(),
    hideReminder: () => manager.hideReminder(),
    handleUserAction: (action: RatingReminderAction) => manager.handleUserAction(action),
    checkAndShowReminder: (delay?: number) => manager.checkAndShowReminder(delay),
    cancelPendingReminder: () => manager.cancelPendingReminder(),
    forceShowReminder: () => manager.forceShowReminder(),
    getReminderStatus: () => manager.getReminderStatus(),
  };
}
