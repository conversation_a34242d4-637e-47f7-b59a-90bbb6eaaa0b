import { Preferences } from '@capacitor/preferences';

// Error handling constants
const FCM_ERROR_PREFS_PREFIX = 'fcm_error_';
const KEY_ERROR_COUNT = `${FCM_ERROR_PREFS_PREFIX}count`;
const KEY_LAST_ERROR = `${FCM_ERROR_PREFS_PREFIX}last_error`;
const KEY_LAST_ERROR_TIME = `${FCM_ERROR_PREFS_PREFIX}last_error_time`;

/**
 * Log levels for FCM operations
 */
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

/**
 * FCM error types
 */
export enum ErrorType {
  TOKEN_RETRIEVAL_FAILED = 'TOKEN_RETRIEVAL_FAILED',
  TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED',
  NOTIFICATION_DISPLAY_FAILED = 'NOTIFICATION_DISPLAY_FAILED',
  MESSAGE_PROCESSING_FAILED = 'MESSAGE_PROCESSING_FAILED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Error type descriptions
 */
const ERROR_TYPE_DESCRIPTIONS: Record<ErrorType, string> = {
  [ErrorType.TOKEN_RETRIEVAL_FAILED]: 'Failed to retrieve FCM token',
  [ErrorType.TOKEN_REFRESH_FAILED]: 'Failed to refresh FCM token',
  [ErrorType.NOTIFICATION_DISPLAY_FAILED]: 'Failed to display notification',
  [ErrorType.PERMISSION_DENIED]: 'Push notification permission denied',
  [ErrorType.NETWORK_ERROR]: 'Network error during FCM operation',
  [ErrorType.MESSAGE_PROCESSING_FAILED]: 'Failed to process FCM message',
  [ErrorType.SERVICE_UNAVAILABLE]: 'FCM service unavailable',
  [ErrorType.UNKNOWN_ERROR]: 'Unknown FCM error'
};

/**
 * Comprehensive error handling and logging utility for FCM operations
 * Provides structured logging, error tracking, and debugging capabilities
 * Mirrors the Android FCMErrorHandler functionality
 */
export class FCMErrorHandler {
  private static instance: FCMErrorHandler;
  private readonly tag = 'FCMErrorHandler';

  /**
   * Get singleton instance
   */
  public static getInstance(): FCMErrorHandler {
    if (!FCMErrorHandler.instance) {
      FCMErrorHandler.instance = new FCMErrorHandler();
    }
    return FCMErrorHandler.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Log a message with specified level
   */
  public log(level: LogLevel, message: string, error?: any): void {
    const timestamp = this.getCurrentTimestamp();
    const logMessage = `[${timestamp}] ${message}`;

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(`${this.tag}: ${logMessage}`, error || '');
        break;
      case LogLevel.INFO:
        console.info(`${this.tag}: ${logMessage}`, error || '');
        break;
      case LogLevel.WARNING:
        console.warn(`${this.tag}: ${logMessage}`, error || '');
        break;
      case LogLevel.ERROR:
        console.error(`${this.tag}: ${logMessage}`, error || '');
        this.recordError(message, error);
        break;
      case LogLevel.CRITICAL:
        console.error(`${this.tag}: CRITICAL: ${logMessage}`, error || '');
        this.recordError(`CRITICAL: ${message}`, error);
        break;
    }
  }

  /**
   * Handle FCM-specific errors
   */
  public async handleFCMError(errorType: ErrorType, details: string, error?: any): Promise<void> {
    const errorMessage = `FCM Error [${ERROR_TYPE_DESCRIPTIONS[errorType]}]: ${details}`;
    this.log(LogLevel.ERROR, errorMessage, error);

    // Additional error-specific handling
    switch (errorType) {
      case ErrorType.TOKEN_RETRIEVAL_FAILED:
        await this.handleTokenRetrievalError(details, error);
        break;
      case ErrorType.TOKEN_REFRESH_FAILED:
        await this.handleTokenRefreshError(details, error);
        break;
      case ErrorType.NOTIFICATION_DISPLAY_FAILED:
        await this.handleNotificationDisplayError(details, error);
        break;
      case ErrorType.PERMISSION_DENIED:
        await this.handlePermissionError(details, error);
        break;
      case ErrorType.NETWORK_ERROR:
        await this.handleNetworkError(details, error);
        break;
      default:
        await this.handleGenericError(errorType, details, error);
        break;
    }
  }

  /**
   * Handle token retrieval errors
   */
  private async handleTokenRetrievalError(details: string, error?: any): Promise<void> {
    this.log(LogLevel.WARNING, 'Token retrieval failed, will retry later');
    // Could implement retry logic here
  }

  /**
   * Handle token refresh errors
   */
  private async handleTokenRefreshError(details: string, error?: any): Promise<void> {
    this.log(LogLevel.WARNING, 'Token refresh failed, marking for retry');
    
    try {
      // Mark token as needing refresh
      await Preferences.set({ key: 'fcm_token_needs_refresh', value: 'true' });
    } catch (prefError) {
      this.log(LogLevel.ERROR, 'Failed to mark token for refresh', prefError);
    }
  }

  /**
   * Handle notification display errors
   */
  private async handleNotificationDisplayError(details: string, error?: any): Promise<void> {
    this.log(LogLevel.ERROR, `Failed to display notification: ${details}`);
    // Could implement fallback notification mechanism
  }

  /**
   * Handle permission errors
   */
  private async handlePermissionError(details: string, error?: any): Promise<void> {
    this.log(LogLevel.WARNING, `Permission denied: ${details}`);
    // Could trigger permission request flow
  }

  /**
   * Handle network errors
   */
  private async handleNetworkError(details: string, error?: any): Promise<void> {
    this.log(LogLevel.WARNING, `Network error: ${details}`);
    // Could implement retry with exponential backoff
  }

  /**
   * Handle generic errors
   */
  private async handleGenericError(errorType: ErrorType, details: string, error?: any): Promise<void> {
    this.log(LogLevel.ERROR, `Generic error [${errorType}]: ${details}`);
  }

  /**
   * Record error for tracking
   */
  private async recordError(message: string, error?: any): Promise<void> {
    try {
      // Increment error count
      const errorCountResult = await Preferences.get({ key: KEY_ERROR_COUNT });
      const errorCount = parseInt(errorCountResult.value || '0', 10);
      await Preferences.set({ key: KEY_ERROR_COUNT, value: (errorCount + 1).toString() });

      // Store last error details
      let errorDetails = message;
      if (error) {
        errorDetails += '\n' + this.getErrorDetails(error);
      }
      await Preferences.set({ key: KEY_LAST_ERROR, value: errorDetails });
      await Preferences.set({ key: KEY_LAST_ERROR_TIME, value: Date.now().toString() });
    } catch (prefError) {
      console.error('Failed to record error:', prefError);
    }
  }

  /**
   * Get error count
   */
  public async getErrorCount(): Promise<number> {
    try {
      const result = await Preferences.get({ key: KEY_ERROR_COUNT });
      return parseInt(result.value || '0', 10);
    } catch (error) {
      this.log(LogLevel.ERROR, 'Failed to get error count', error);
      return 0;
    }
  }

  /**
   * Get last error
   */
  public async getLastError(): Promise<string | null> {
    try {
      const result = await Preferences.get({ key: KEY_LAST_ERROR });
      return result.value;
    } catch (error) {
      this.log(LogLevel.ERROR, 'Failed to get last error', error);
      return null;
    }
  }

  /**
   * Get last error time
   */
  public async getLastErrorTime(): Promise<number> {
    try {
      const result = await Preferences.get({ key: KEY_LAST_ERROR_TIME });
      return parseInt(result.value || '0', 10);
    } catch (error) {
      this.log(LogLevel.ERROR, 'Failed to get last error time', error);
      return 0;
    }
  }

  /**
   * Clear error statistics
   */
  public async clearErrorStats(): Promise<void> {
    try {
      await Preferences.remove({ key: KEY_ERROR_COUNT });
      await Preferences.remove({ key: KEY_LAST_ERROR });
      await Preferences.remove({ key: KEY_LAST_ERROR_TIME });
      this.log(LogLevel.INFO, 'Error statistics cleared');
    } catch (error) {
      this.log(LogLevel.ERROR, 'Failed to clear error statistics', error);
    }
  }

  /**
   * Check if error rate is too high
   */
  public async isErrorRateTooHigh(): Promise<boolean> {
    try {
      const errorCount = await this.getErrorCount();
      const lastErrorTime = await this.getLastErrorTime();
      const currentTime = Date.now();

      // Consider error rate too high if more than 10 errors in the last hour
      return errorCount > 10 && (currentTime - lastErrorTime) < 3600000; // 1 hour
    } catch (error) {
      this.log(LogLevel.ERROR, 'Failed to check error rate', error);
      return false;
    }
  }

  /**
   * Log FCM operation start
   */
  public logOperationStart(operation: string): void {
    this.log(LogLevel.DEBUG, `Starting FCM operation: ${operation}`);
  }

  /**
   * Log FCM operation success
   */
  public logOperationSuccess(operation: string): void {
    this.log(LogLevel.INFO, `FCM operation completed successfully: ${operation}`);
  }

  /**
   * Log FCM operation failure
   */
  public async logOperationFailure(operation: string, error?: any): Promise<void> {
    await this.handleFCMError(ErrorType.UNKNOWN_ERROR, `Operation failed: ${operation}`, error);
  }

  /**
   * Create error report for debugging
   */
  public async createErrorReport(): Promise<string> {
    try {
      const errorCount = await this.getErrorCount();
      const lastError = await this.getLastError();
      const lastErrorTime = await this.getLastErrorTime();
      const isErrorRateTooHigh = await this.isErrorRateTooHigh();

      const report = [
        '=== FCM Error Report ===',
        `Timestamp: ${this.getCurrentTimestamp()}`,
        `Total Errors: ${errorCount}`,
        `Last Error Time: ${new Date(lastErrorTime).toISOString()}`,
        `Last Error Details:\n${lastError || 'None'}`,
        `Error Rate Too High: ${isErrorRateTooHigh}`,
        '========================'
      ].join('\n');

      return report;
    } catch (error) {
      this.log(LogLevel.ERROR, 'Failed to create error report', error);
      return 'Failed to generate error report';
    }
  }

  /**
   * Get current timestamp
   */
  private getCurrentTimestamp(): string {
    return new Date().toISOString();
  }

  /**
   * Get error details from error object
   */
  private getErrorDetails(error: any): string {
    if (error instanceof Error) {
      return `${error.name}: ${error.message}\n${error.stack || ''}`;
    } else if (typeof error === 'string') {
      return error;
    } else {
      return JSON.stringify(error, null, 2);
    }
  }
}

// Export singleton instance
export const fcmErrorHandler = FCMErrorHandler.getInstance();
