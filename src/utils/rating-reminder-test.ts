import { ratingReminderService, RatingReminderAction, UserRatingPreference } from '../services/rating-reminder-service';
import { ratingReminderManager } from '../services/rating-reminder-manager';
import { ratingReminderLogger } from './rating-reminder-logger';

/**
 * Comprehensive test suite for Rating Reminder functionality
 * This utility helps test all user flows and edge cases
 */
export class RatingReminderTest {
  private static instance: RatingReminderTest;
  
  public static getInstance(): RatingReminderTest {
    if (!RatingReminderTest.instance) {
      RatingReminderTest.instance = new RatingReminderTest();
    }
    return RatingReminderTest.instance;
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<TestResults> {
    console.log('🧪 Starting Rating Reminder Test Suite...');
    
    const results: TestResults = {
      passed: 0,
      failed: 0,
      tests: []
    };

    // Test 1: Service Initialization
    await this.runTest('Service Initialization', async () => {
      await ratingReminderService.initialize();
      const status = ratingReminderService.getReminderStatus();
      if (!status.isInitialized) {
        throw new Error('Service not initialized');
      }
    }, results);

    // Test 2: Launch Tracking
    await this.runTest('Launch Tracking', async () => {
      await ratingReminderService.resetState();
      const initialCount = ratingReminderService.getState().launchCount;
      await ratingReminderService.trackLaunch();
      const newCount = ratingReminderService.getState().launchCount;
      if (newCount !== initialCount + 1) {
        throw new Error(`Launch count not incremented: ${initialCount} -> ${newCount}`);
      }
    }, results);

    // Test 3: Threshold Logic
    await this.runTest('Threshold Logic', async () => {
      await ratingReminderService.resetState();
      const config = ratingReminderService.getConfig();
      
      // Should not show before threshold
      for (let i = 0; i < config.launchThreshold - 1; i++) {
        await ratingReminderService.trackLaunch();
      }
      if (ratingReminderService.shouldShowReminder()) {
        throw new Error('Reminder showing before threshold');
      }
      
      // Should show at threshold
      await ratingReminderService.trackLaunch();
      if (!ratingReminderService.shouldShowReminder()) {
        throw new Error('Reminder not showing at threshold');
      }
    }, results);

    // Test 4: User Actions
    await this.runTest('Rate Now Action', async () => {
      await ratingReminderService.resetState();
      await ratingReminderService.simulateLaunches(10);
      await ratingReminderService.handleUserAction(RatingReminderAction.RATE_NOW);
      
      const state = ratingReminderService.getState();
      if (state.userPreference !== UserRatingPreference.RATED || !state.hasRated) {
        throw new Error('Rate now action not handled correctly');
      }
      
      if (ratingReminderService.shouldShowReminder()) {
        throw new Error('Reminder still showing after rating');
      }
    }, results);

    // Test 5: Remind Later Action
    await this.runTest('Remind Later Action', async () => {
      await ratingReminderService.resetState();
      await ratingReminderService.simulateLaunches(10);
      await ratingReminderService.handleUserAction(RatingReminderAction.REMIND_LATER);
      
      const state = ratingReminderService.getState();
      if (state.userPreference !== UserRatingPreference.REMIND_LATER) {
        throw new Error('Remind later action not handled correctly');
      }
      
      if (ratingReminderService.shouldShowReminder()) {
        throw new Error('Reminder showing immediately after remind later');
      }
    }, results);

    // Test 6: Don't Ask Again Action
    await this.runTest('Don\'t Ask Again Action', async () => {
      await ratingReminderService.resetState();
      await ratingReminderService.simulateLaunches(10);
      await ratingReminderService.handleUserAction(RatingReminderAction.DONT_ASK_AGAIN);
      
      const state = ratingReminderService.getState();
      if (state.userPreference !== UserRatingPreference.DONT_ASK_AGAIN) {
        throw new Error('Don\'t ask again action not handled correctly');
      }
      
      // Should not show even with more launches
      await ratingReminderService.simulateLaunches(100);
      if (ratingReminderService.shouldShowReminder()) {
        throw new Error('Reminder showing after don\'t ask again');
      }
    }, results);

    // Test 7: Snooze Logic
    await this.runTest('Snooze Logic', async () => {
      await ratingReminderService.resetState();
      await ratingReminderService.simulateLaunches(10);
      await ratingReminderService.handleUserAction(RatingReminderAction.REMIND_LATER);
      
      const config = ratingReminderService.getConfig();
      
      // Should not show during snooze period
      for (let i = 0; i < config.snoozeLaunches - 1; i++) {
        await ratingReminderService.trackLaunch();
        if (ratingReminderService.shouldShowReminder()) {
          throw new Error(`Reminder showing during snooze period at launch ${i + 1}`);
        }
      }
      
      // Should show after snooze period
      await ratingReminderService.trackLaunch();
      if (!ratingReminderService.shouldShowReminder()) {
        throw new Error('Reminder not showing after snooze period');
      }
    }, results);

    // Test 8: Settings Integration
    await this.runTest('Settings Integration', async () => {
      // Test enabling/disabling via settings
      await ratingReminderService.setUserSettingsEnabled(false);
      if (ratingReminderService.isEnabled()) {
        throw new Error('Service still enabled after user disabled');
      }
      
      await ratingReminderService.setUserSettingsEnabled(true);
      if (!ratingReminderService.isEnabled()) {
        throw new Error('Service not enabled after user enabled');
      }
      
      // Reset to default
      await ratingReminderService.setUserSettingsEnabled(true);
    }, results);

    // Test 9: State Persistence
    await this.runTest('State Persistence', async () => {
      await ratingReminderService.resetState();
      await ratingReminderService.simulateLaunches(5);
      const originalState = ratingReminderService.getState();
      
      // Reinitialize service to test persistence
      await ratingReminderService.initialize();
      const loadedState = ratingReminderService.getState();
      
      if (loadedState.launchCount !== originalState.launchCount) {
        throw new Error('Launch count not persisted correctly');
      }
    }, results);

    // Test 10: Manager Integration
    await this.runTest('Manager Integration', async () => {
      await ratingReminderService.resetState();
      await ratingReminderService.simulateLaunches(10);
      
      const status = ratingReminderManager.getReminderStatus();
      if (!status.shouldShow) {
        throw new Error('Manager not detecting reminder should show');
      }
      
      // Test modal state
      if (ratingReminderManager.isModalOpen.value) {
        throw new Error('Modal should not be open initially');
      }
    }, results);

    console.log(`🧪 Test Suite Complete: ${results.passed} passed, ${results.failed} failed`);
    return results;
  }

  /**
   * Test specific user flow
   */
  async testUserFlow(flow: UserFlow): Promise<boolean> {
    try {
      console.log(`🧪 Testing user flow: ${flow}`);
      
      switch (flow) {
        case UserFlow.FIRST_TIME_USER:
          return await this.testFirstTimeUser();
        case UserFlow.RETURNING_USER:
          return await this.testReturningUser();
        case UserFlow.RATE_NOW:
          return await this.testRateNowFlow();
        case UserFlow.REMIND_LATER:
          return await this.testRemindLaterFlow();
        case UserFlow.DONT_ASK_AGAIN:
          return await this.testDontAskAgainFlow();
        case UserFlow.SETTINGS_DISABLE:
          return await this.testSettingsDisableFlow();
        default:
          throw new Error(`Unknown user flow: ${flow}`);
      }
    } catch (error) {
      console.error(`❌ User flow test failed: ${flow}`, error);
      return false;
    }
  }

  /**
   * Run individual test
   */
  private async runTest(
    name: string, 
    testFn: () => Promise<void>, 
    results: TestResults
  ): Promise<void> {
    try {
      console.log(`🧪 Running test: ${name}`);
      await testFn();
      console.log(`✅ Test passed: ${name}`);
      results.passed++;
      results.tests.push({ name, passed: true });
    } catch (error) {
      console.error(`❌ Test failed: ${name}`, error);
      results.failed++;
      results.tests.push({ name, passed: false, error: error as Error });
    }
  }

  /**
   * Test first-time user flow
   */
  private async testFirstTimeUser(): Promise<boolean> {
    await ratingReminderService.resetState();
    const config = ratingReminderService.getConfig();
    
    // Simulate launches up to threshold
    for (let i = 0; i < config.launchThreshold; i++) {
      await ratingReminderService.trackLaunch();
    }
    
    return ratingReminderService.shouldShowReminder();
  }

  /**
   * Test returning user flow
   */
  private async testReturningUser(): Promise<boolean> {
    await ratingReminderService.resetState();
    await ratingReminderService.simulateLaunches(2);
    
    // User should not see reminder yet
    return !ratingReminderService.shouldShowReminder();
  }

  /**
   * Test rate now flow
   */
  private async testRateNowFlow(): Promise<boolean> {
    await ratingReminderService.resetState();
    await ratingReminderService.simulateLaunches(10);
    await ratingReminderService.handleUserAction(RatingReminderAction.RATE_NOW);
    
    const state = ratingReminderService.getState();
    return state.hasRated && !ratingReminderService.shouldShowReminder();
  }

  /**
   * Test remind later flow
   */
  private async testRemindLaterFlow(): Promise<boolean> {
    await ratingReminderService.resetState();
    await ratingReminderService.simulateLaunches(10);
    await ratingReminderService.handleUserAction(RatingReminderAction.REMIND_LATER);
    
    // Should not show immediately
    if (ratingReminderService.shouldShowReminder()) {
      return false;
    }
    
    // Should show after snooze period
    const config = ratingReminderService.getConfig();
    for (let i = 0; i < config.snoozeLaunches; i++) {
      await ratingReminderService.trackLaunch();
    }
    
    return ratingReminderService.shouldShowReminder();
  }

  /**
   * Test don't ask again flow
   */
  private async testDontAskAgainFlow(): Promise<boolean> {
    await ratingReminderService.resetState();
    await ratingReminderService.simulateLaunches(10);
    await ratingReminderService.handleUserAction(RatingReminderAction.DONT_ASK_AGAIN);
    
    // Should never show again
    await ratingReminderService.simulateLaunches(100);
    return !ratingReminderService.shouldShowReminder();
  }

  /**
   * Test settings disable flow
   */
  private async testSettingsDisableFlow(): Promise<boolean> {
    await ratingReminderService.setUserSettingsEnabled(false);
    await ratingReminderService.resetState();
    await ratingReminderService.simulateLaunches(100);
    
    const disabled = !ratingReminderService.shouldShowReminder();
    
    // Re-enable for other tests
    await ratingReminderService.setUserSettingsEnabled(true);
    
    return disabled;
  }

  /**
   * Get debug information
   */
  getDebugInfo(): any {
    return {
      serviceStatus: ratingReminderService.getReminderStatus(),
      managerStatus: ratingReminderManager.getReminderStatus(),
      logs: ratingReminderLogger.getRecentLogs(20),
      logStats: ratingReminderLogger.getLogStats()
    };
  }
}

// Types
export interface TestResults {
  passed: number;
  failed: number;
  tests: Array<{ name: string; passed: boolean; error?: Error }>;
}

export enum UserFlow {
  FIRST_TIME_USER = 'first_time_user',
  RETURNING_USER = 'returning_user',
  RATE_NOW = 'rate_now',
  REMIND_LATER = 'remind_later',
  DONT_ASK_AGAIN = 'dont_ask_again',
  SETTINGS_DISABLE = 'settings_disable'
}

// Export singleton instance
export const ratingReminderTest = RatingReminderTest.getInstance();
