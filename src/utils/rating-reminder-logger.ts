/**
 * Dedicated logger for Rating Reminder system
 * Provides structured logging with different levels and context
 */

export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

export interface LogContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  [key: string]: any;
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: LogContext;
  error?: Error;
  stack?: string;
}

class RatingReminderLogger {
  private static instance: RatingReminderLogger;
  private logs: LogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 log entries
  private isDevelopment = import.meta.env.DEV;

  public static getInstance(): RatingReminderLogger {
    if (!RatingReminderLogger.instance) {
      RatingReminderLogger.instance = new RatingReminderLogger();
    }
    return RatingReminderLogger.instance;
  }

  private constructor() {
    // Private constructor for singleton
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: LogContext): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Log info message
   */
  info(message: string, context?: LogContext): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: LogContext): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Log error message
   */
  error(message: string, error?: Error, context?: LogContext): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  /**
   * Log operation start
   */
  operationStart(operation: string, context?: LogContext): void {
    this.info(`Starting operation: ${operation}`, {
      ...context,
      action: 'operation_start',
      operation
    });
  }

  /**
   * Log operation success
   */
  operationSuccess(operation: string, context?: LogContext): void {
    this.info(`Operation completed successfully: ${operation}`, {
      ...context,
      action: 'operation_success',
      operation
    });
  }

  /**
   * Log operation failure
   */
  operationFailure(operation: string, error: Error, context?: LogContext): void {
    this.error(`Operation failed: ${operation}`, error, {
      ...context,
      action: 'operation_failure',
      operation
    });
  }

  /**
   * Log user action
   */
  userAction(action: string, context?: LogContext): void {
    this.info(`User action: ${action}`, {
      ...context,
      action: 'user_action',
      userAction: action
    });
  }

  /**
   * Log state change
   */
  stateChange(from: any, to: any, context?: LogContext): void {
    this.info('State change', {
      ...context,
      action: 'state_change',
      fromState: from,
      toState: to
    });
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, context?: LogContext, error?: Error): void {
    const timestamp = new Date().toISOString();
    
    const logEntry: LogEntry = {
      timestamp,
      level,
      message,
      context,
      error,
      stack: error?.stack
    };

    // Add to internal log storage
    this.addToStorage(logEntry);

    // Console output
    this.outputToConsole(logEntry);
  }

  /**
   * Add log entry to internal storage
   */
  private addToStorage(entry: LogEntry): void {
    this.logs.push(entry);
    
    // Trim logs if exceeding max
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  /**
   * Output log entry to console
   */
  private outputToConsole(entry: LogEntry): void {
    const prefix = `[RatingReminder] ${entry.timestamp} ${entry.level}:`;
    const message = `${prefix} ${entry.message}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        if (this.isDevelopment) {
          console.debug(message, entry.context, entry.error);
        }
        break;
      case LogLevel.INFO:
        console.log(message, entry.context);
        break;
      case LogLevel.WARN:
        console.warn(message, entry.context, entry.error);
        break;
      case LogLevel.ERROR:
        console.error(message, entry.context, entry.error);
        break;
    }
  }

  /**
   * Get recent logs
   */
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logs.slice(-count);
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * Get error logs
   */
  getErrorLogs(): LogEntry[] {
    return this.getLogsByLevel(LogLevel.ERROR);
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logs = [];
    this.info('Logs cleared');
  }

  /**
   * Export logs as JSON
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  /**
   * Get log statistics
   */
  getLogStats(): {
    total: number;
    byLevel: Record<LogLevel, number>;
    errorCount: number;
    warningCount: number;
  } {
    const byLevel = {
      [LogLevel.DEBUG]: 0,
      [LogLevel.INFO]: 0,
      [LogLevel.WARN]: 0,
      [LogLevel.ERROR]: 0
    };

    this.logs.forEach(log => {
      byLevel[log.level]++;
    });

    return {
      total: this.logs.length,
      byLevel,
      errorCount: byLevel[LogLevel.ERROR],
      warningCount: byLevel[LogLevel.WARN]
    };
  }

  /**
   * Create error with context
   */
  createError(message: string, context?: LogContext): Error {
    const error = new Error(message);
    
    // Add context to error for better debugging
    if (context) {
      (error as any).context = context;
    }
    
    return error;
  }

  /**
   * Wrap async operation with logging
   */
  async wrapOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    context?: LogContext
  ): Promise<T> {
    this.operationStart(operation, context);
    
    try {
      const result = await fn();
      this.operationSuccess(operation, context);
      return result;
    } catch (error) {
      this.operationFailure(operation, error as Error, context);
      throw error;
    }
  }
}

// Export singleton instance
export const ratingReminderLogger = RatingReminderLogger.getInstance();

// Convenience functions
export const logDebug = (message: string, context?: LogContext) => 
  ratingReminderLogger.debug(message, context);

export const logInfo = (message: string, context?: LogContext) => 
  ratingReminderLogger.info(message, context);

export const logWarn = (message: string, context?: LogContext) => 
  ratingReminderLogger.warn(message, context);

export const logError = (message: string, error?: Error, context?: LogContext) => 
  ratingReminderLogger.error(message, error, context);

export const logUserAction = (action: string, context?: LogContext) => 
  ratingReminderLogger.userAction(action, context);

export const logStateChange = (from: any, to: any, context?: LogContext) => 
  ratingReminderLogger.stateChange(from, to, context);

export const wrapOperation = <T>(
  operation: string,
  fn: () => Promise<T>,
  context?: LogContext
) => ratingReminderLogger.wrapOperation(operation, fn, context);
