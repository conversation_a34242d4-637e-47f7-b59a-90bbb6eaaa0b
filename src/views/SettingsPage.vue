<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/container"></ion-back-button>
        </ion-buttons>
        <ion-title>Settings</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">Settings</ion-title>
        </ion-toolbar>
      </ion-header>

      <!-- Settings List -->
      <ion-list>
        <!-- App Information Section -->
        <ion-list-header>
          <ion-label>App Information</ion-label>
        </ion-list-header>

        <ion-item>
          <ion-icon :icon="informationCircleOutline" slot="start"></ion-icon>
          <ion-label>
            <h2>{{ appName }}</h2>
            <p>Version {{ appVersion }}</p>
          </ion-label>
        </ion-item>

        <!-- Preferences Section -->
        <ion-list-header>
          <ion-label>Preferences</ion-label>
        </ion-list-header>

        <ion-item>
          <ion-icon :icon="starOutline" slot="start"></ion-icon>
          <ion-label>
            <h2>App Rating Reminders</h2>
            <p v-if="ratingRemindersEnabled">
              Show reminders to rate the app after {{ launchThreshold }} launches
            </p>
            <p v-else>
              Rating reminders are disabled
            </p>
          </ion-label>
          <ion-toggle
            :checked="ratingRemindersEnabled"
            @ion-change="handleRatingReminderToggle"
            :disabled="isLoading"
            slot="end"
            :aria-label="ratingRemindersEnabled ? 'Disable rating reminders' : 'Enable rating reminders'"
          ></ion-toggle>
        </ion-item>

        <!-- Debug Section (only in development) -->
        <template v-if="isDevelopment">
          <ion-list-header>
            <ion-label>Debug</ion-label>
          </ion-list-header>

          <ion-item button @click="showRatingReminderDebug">
            <ion-icon :icon="bugOutline" slot="start"></ion-icon>
            <ion-label>
              <h2>Rating Reminder Debug</h2>
              <p>View rating reminder status and controls</p>
            </ion-label>
            <ion-icon :icon="chevronForwardOutline" slot="end"></ion-icon>
          </ion-item>

          <ion-item button @click="runRatingReminderTests">
            <ion-icon :icon="checkmarkCircleOutline" slot="start"></ion-icon>
            <ion-label>
              <h2>Run Rating Reminder Tests</h2>
              <p>Test all user flows and edge cases</p>
            </ion-label>
            <ion-icon :icon="chevronForwardOutline" slot="end"></ion-icon>
          </ion-item>
        </template>

        <!-- About Section -->
        <ion-list-header>
          <ion-label>About</ion-label>
        </ion-list-header>

        <ion-item>
          <ion-icon :icon="shieldCheckmarkOutline" slot="start"></ion-icon>
          <ion-label>
            <h2>Privacy</h2>
            <p>Your data stays on your device</p>
          </ion-label>
        </ion-item>

        <ion-item>
          <ion-icon :icon="documentTextOutline" slot="start"></ion-icon>
          <ion-label>
            <h2>Open Source</h2>
            <p>Built with Ionic Vue and Capacitor</p>
          </ion-label>
        </ion-item>
      </ion-list>

      <!-- Loading indicator -->
      <div v-if="isLoading" class="loading-container">
        <ion-spinner name="crescent"></ion-spinner>
        <p>Updating settings...</p>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonListHeader,
  IonItem,
  IonLabel,
  IonToggle,
  IonIcon,
  IonButtons,
  IonBackButton,
  IonSpinner,
  alertController,
  toastController,
} from '@ionic/vue';
import {
  starOutline,
  informationCircleOutline,
  bugOutline,
  chevronForwardOutline,
  shieldCheckmarkOutline,
  documentTextOutline,
  checkmarkCircleOutline,
} from 'ionicons/icons';
import { ratingReminderService } from '../services/rating-reminder-service';
import { logInfo, logWarn, wrapOperation } from '../utils/rating-reminder-logger';
import { ratingReminderTest } from '../utils/rating-reminder-test';

// State
const isLoading = ref(false);
const ratingRemindersEnabled = ref(true);

// Computed
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'Web2App');
const appVersion = computed(() => '1.0.0'); // TODO: Get from package.json or build process
const isDevelopment = computed(() => import.meta.env.DEV);
const launchThreshold = computed(() => {
  try {
    return ratingReminderService.getConfig().launchThreshold;
  } catch {
    return parseInt(import.meta.env.VITE_RATING_REMINDER_LAUNCH_THRESHOLD) || 5;
  }
});

// Methods
const loadSettings = async () => {
  return wrapOperation('loadSettings', async () => {
    isLoading.value = true;

    // Ensure rating reminder service is initialized
    if (!ratingReminderService.isEnabled) {
      logWarn('Rating reminder service not properly initialized', {
        component: 'SettingsPage'
      });
    }

    // Load rating reminder setting
    const userSetting = ratingReminderService.getUserSettingsEnabled();

    // If user hasn't set a preference, default to the environment setting
    if (userSetting !== null) {
      ratingRemindersEnabled.value = userSetting;
    } else {
      // Use the environment default
      const config = ratingReminderService.getConfig();
      ratingRemindersEnabled.value = config.enabled;
    }

    logInfo('Settings loaded successfully', {
      component: 'SettingsPage',
      userSetting,
      finalValue: ratingRemindersEnabled.value
    });

  }, { component: 'SettingsPage' }).catch(async (error) => {
    await showErrorToast('Failed to load settings');
    throw error;
  }).finally(() => {
    isLoading.value = false;
  });
};

const handleRatingReminderToggle = async (event: CustomEvent) => {
  const enabled = event.detail.checked;
  const previousValue = ratingRemindersEnabled.value;

  return wrapOperation('handleRatingReminderToggle', async () => {
    isLoading.value = true;

    // Optimistically update the UI
    ratingRemindersEnabled.value = enabled;

    // Save the setting
    await ratingReminderService.setUserSettingsEnabled(enabled);

    // Provide user feedback
    const message = enabled
      ? 'Rating reminders enabled'
      : 'Rating reminders disabled';
    await showSuccessToast(message);

    logInfo('Rating reminder setting updated successfully', {
      component: 'SettingsPage',
      enabled,
      previousValue,
      serviceEnabled: ratingReminderService.isEnabled()
    });

  }, {
    component: 'SettingsPage',
    enabled,
    previousValue
  }).catch(async (error) => {
    // Revert toggle state on error
    ratingRemindersEnabled.value = previousValue;
    await showErrorToast('Failed to update setting. Please try again.');
    throw error;
  }).finally(() => {
    isLoading.value = false;
  });
};

const showRatingReminderDebug = async () => {
  try {
    const status = ratingReminderService.getReminderStatus();
    const stats = ratingReminderService.getLaunchStats();
    
    const debugInfo = `
Rating Reminder Debug Info:

Status:
- Enabled: ${status.isEnabled}
- Initialized: ${status.isInitialized}
- Should Track: ${status.shouldTrack}
- Should Show: ${status.shouldShow}

Launch Stats:
- Current Count: ${stats.currentCount}
- Threshold: ${stats.threshold}
- Remaining: ${stats.remaining}

User Preference: ${status.state.userPreference}
Has Rated: ${status.state.hasRated}

Blocking Reasons:
${status.blockingReasons.map(reason => `- ${reason}`).join('\n')}
    `.trim();

    const alert = await alertController.create({
      header: 'Rating Reminder Debug',
      message: debugInfo,
      buttons: [
        {
          text: 'Reset State',
          role: 'destructive',
          handler: async () => {
            try {
              await ratingReminderService.resetState();
              await showSuccessToast('Rating reminder state reset');
            } catch (error) {
              console.error('Failed to reset state:', error);
              await showErrorToast('Failed to reset state');
            }
          }
        },
        {
          text: 'Force Show',
          handler: async () => {
            try {
              const canShow = await ratingReminderService.forceShowReminder();
              const message = canShow 
                ? 'Reminder can be shown (check console)' 
                : 'Reminder cannot be shown';
              await showSuccessToast(message);
            } catch (error) {
              console.error('Failed to force show:', error);
              await showErrorToast('Failed to force show');
            }
          }
        },
        {
          text: 'Close',
          role: 'cancel'
        }
      ]
    });

    await alert.present();
  } catch (error) {
    console.error('Failed to show debug info:', error);
    await showErrorToast('Failed to load debug info');
  }
};

const showSuccessToast = async (message: string) => {
  const toast = await toastController.create({
    message,
    duration: 2000,
    color: 'success',
    position: 'bottom'
  });
  await toast.present();
};

const showErrorToast = async (message: string) => {
  const toast = await toastController.create({
    message,
    duration: 3000,
    color: 'danger',
    position: 'bottom'
  });
  await toast.present();
};

const runRatingReminderTests = async () => {
  try {
    const alert = await alertController.create({
      header: 'Running Tests',
      message: 'Testing all rating reminder functionality...',
      buttons: ['OK']
    });
    await alert.present();

    // Run the comprehensive test suite
    const results = await ratingReminderTest.runAllTests();

    const resultMessage = `
Test Results:
✅ Passed: ${results.passed}
❌ Failed: ${results.failed}

${results.tests.map(test =>
  `${test.passed ? '✅' : '❌'} ${test.name}${test.error ? ': ' + test.error.message : ''}`
).join('\n')}
    `.trim();

    const resultAlert = await alertController.create({
      header: 'Test Results',
      message: resultMessage,
      buttons: [
        {
          text: 'View Debug Info',
          handler: async () => {
            const debugInfo = ratingReminderTest.getDebugInfo();
            console.log('Rating Reminder Debug Info:', debugInfo);
            await showSuccessToast('Debug info logged to console');
          }
        },
        {
          text: 'Close',
          role: 'cancel'
        }
      ]
    });

    await alert.dismiss();
    await resultAlert.present();

    if (results.failed === 0) {
      await showSuccessToast('All tests passed! 🎉');
    } else {
      await showErrorToast(`${results.failed} tests failed. Check console for details.`);
    }

  } catch (error) {
    console.error('Failed to run tests:', error);
    await showErrorToast('Failed to run tests');
  }
};

// Lifecycle
onMounted(async () => {
  await loadSettings();
});
</script>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: var(--ion-color-medium);
  font-size: 14px;
}

/* Ensure proper spacing for list headers */
ion-list-header {
  padding-top: 24px;
}

ion-list-header:first-child {
  padding-top: 16px;
}

/* Style for debug section */
.debug-section {
  opacity: 0.8;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  ion-content {
    --padding-start: 20px;
    --padding-end: 20px;
  }
}
</style>
