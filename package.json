{"name": "web2app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test:e2e": "cypress run", "test:unit": "vitest", "lint": "eslint . --fix", "format": "prettier --write . && npm run lint", "ls-lint": "ls-lint", "ionic:build": "npm run build", "ionic:serve": "npm run dev -- --open", "build:ios": "npm run build && npx cap sync ios && npx cap build ios", "build:android": "npm run build && npx cap sync android && npx cap build android", "sync": "npx cap sync", "sync:ios": "npx cap sync ios", "sync:android": "npx cap sync android"}, "dependencies": {"@capacitor-community/privacy-screen": "^6.0.0", "@capacitor/android": "7.4.2", "@capacitor/app": "7.0.1", "@capacitor/core": "7.4.2", "@capacitor/dialog": "^7.0.1", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "7.4.2", "@capacitor/keyboard": "7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@capawesome/capacitor-app-review": "^7.0.1", "@capgo/inappbrowser": "^7.12.1", "@ionic/vue": "^8.0.0", "@ionic/vue-router": "^8.0.0", "@vue/eslint-config-prettier": "^10.2.0", "dotenv": "^17.2.0", "ionicons": "^7.0.0", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "7.4.2", "@vitejs/plugin-legacy": "^5.0.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.3.0", "cypress": "^13.5.0", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.33.0", "jsdom": "^22.1.0", "terser": "^5.4.0", "typescript": "~5.6.2", "vite": "~5.2.0", "vitest": "^0.34.6", "vue-tsc": "^2.1.10"}, "overrides": {"rollup": "4.44.0"}}